#!/usr/bin/env python3
"""
Cross-Category Indent Calculation Breakdown
==========================================

Shows detailed calculation breakdown for a specific category's cross-category indent value.
Provides step-by-step explanation of how the final value is calculated.

Usage:
    python cross_category_indent_validator.py --csv path/to/consumption.csv --config path/to/config.json --category "CATEGORY_NAME"
    python cross_category_indent_validator.py --csv path/to/consumption.csv --tenant-id your_tenant_id --category "CATEGORY_NAME"
"""

import pandas as pd
import json
import argparse
import sys
from typing import Dict, List, Set, Tuple

def load_mapping_config(config_path: str = None, tenant_id: str = None) -> Dict:
    """
    Load mapping configuration from file or database.
    
    Args:
        config_path: Path to JSON configuration file
        tenant_id: Tenant ID for database lookup
        
    Returns:
        Mapping configuration dictionary
    """
    if config_path:
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                print(f"✓ Loaded configuration from: {config_path}")
                return config
        except FileNotFoundError:
            print(f"✗ Configuration file not found: {config_path}")
        except json.JSONDecodeError as e:
            print(f"✗ Error parsing JSON: {e}")
    
    if tenant_id:
        # In a real implementation, you would connect to your database here
        print(f"Database lookup for tenant '{tenant_id}' not implemented in validator")
        print("Please provide configuration file with --config parameter")
    
    return None

def build_category_workarea_mappings(mapping_config: Dict) -> List[Dict]:
    """
    Build category-workarea mappings from configuration using dashboard logic.

    Args:
        mapping_config: Full mapping configuration

    Returns:
        List of category-workarea mappings
    """
    if not mapping_config:
        return []

    # Handle nested config structure (mappingConfig wrapper)
    config = mapping_config.get('mappingConfig', mapping_config)

    # Extract mappings from config structure
    group_to_categories = {}
    group_to_workareas = {}

    # Build group mappings
    for group_mapping in config.get('departmentGroupCategoryMappings', []):
        group_id = group_mapping.get('groupId')
        group_name = group_mapping.get('groupName')
        categories = group_mapping.get('categories', [])

        if group_id and categories:
            group_to_categories[group_name] = {'id': group_id, 'categories': categories}

    for group_mapping in config.get('departmentGroupWorkareaMappings', []):
        group_id = group_mapping.get('groupId')
        group_name = group_mapping.get('groupName')
        workareas = group_mapping.get('workAreas', [])

        if group_id and workareas:
            group_to_workareas[group_name] = {'id': group_id, 'workareas': workareas}
    
    # Always add OTHERS group (dashboard logic)
    group_to_categories['OTHERS'] = {'id': 'OTHERS_GROUP', 'categories': ['OTHERS']}
    group_to_workareas['OTHERS'] = {'id': 'OTHERS_GROUP', 'workareas': ['OTHERS_WORKAREA']}
    
    # Build category-workarea mappings
    category_workarea_mappings = []
    for group_name, group_info in group_to_categories.items():
        if group_name in group_to_workareas:
            categories = group_info['categories']
            workareas = group_to_workareas[group_name]['workareas']
            for category in categories:
                category_workarea_mappings.append({
                    'categoryName': category,
                    'workAreas': workareas
                })
    
    return category_workarea_mappings

def calculate_cross_category_indents(consumption_df: pd.DataFrame, category_workarea_mappings: List[Dict]) -> Dict:
    """
    Calculate cross-category indents using exact dashboard logic.
    
    Args:
        consumption_df: Consumption dataframe
        category_workarea_mappings: Category-workarea mappings
        
    Returns:
        Dictionary with cross-category indent results
    """
    if consumption_df.empty or not category_workarea_mappings:
        return {}
    
    # Build mapped categories set and category-to-workareas mapping
    mapped_categories = set()
    category_to_workareas = {}
    
    for mapping in category_workarea_mappings:
        category_name = mapping['categoryName']
        work_areas = mapping['workAreas']
        if category_name and work_areas:
            mapped_categories.add(category_name)
            category_to_workareas[category_name] = work_areas
    
    # Get mapped category names (excluding OTHERS)
    mapped_category_names = {cat for cat in mapped_categories if cat != 'OTHERS'}
    
    # Collect indent data by workarea
    indent_data = {}  # {workarea: {category: {subcategory: indent_value}}}
    transfer_details_log = []
    
    # Process consumption data
    for _, row in consumption_df.iterrows():
        original_category = str(row.get('Category', 'Others')).strip()
        category = original_category
        subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
        workarea = str(row.get('WorkArea', '')).strip()
        
        # Skip invalid entries (exact dashboard logic)
        if (not category or category == 'nan' or category == 'None' or
            not subcategory or subcategory == 'nan' or subcategory == 'None' or
            not workarea):
            continue
        
        # Skip cross-category processing for unmapped categories (dashboard logic)
        if category not in mapped_category_names:
            continue
        
        # Extract indent data
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in indent_data:
                    indent_data[workarea] = {}
                if category not in indent_data[workarea]:
                    indent_data[workarea][category] = {}
                if subcategory not in indent_data[workarea][category]:
                    indent_data[workarea][category][subcategory] = 0
                indent_data[workarea][category][subcategory] += indent_value
                
        except (ValueError, TypeError):
            continue
    
    # Initialize reconciliation impact tracking
    reconciliation_impact = {}
    
    # Process cross-category flows (exact dashboard logic)
    for workarea, workarea_categories in indent_data.items():
        # Find ALL categories that have workareas mapping to this workarea
        workarea_owner_categories = []
        
        for category_name, work_areas in category_to_workareas.items():
            if workarea in work_areas:
                workarea_owner_categories.append(category_name)
        
        # If no owners found, skip this workarea
        if not workarea_owner_categories:
            continue
        
        # For each category that has indents in this workarea, check if it's cross-category
        for indent_category, subcategories in workarea_categories.items():
            for subcategory, indent_value in subcategories.items():
                # Check if this is cross-category
                cross_category_owners = [owner for owner in workarea_owner_categories if owner != indent_category]
                
                if not cross_category_owners:
                    # Same category indent - track for display but no reconciliation impact
                    continue
                
                # Initialize categories in reconciliation impact
                if indent_category not in reconciliation_impact:
                    reconciliation_impact[indent_category] = {
                        'cross_category_indents': 0,
                        'transactions': []
                    }
                
                for receiving_category in cross_category_owners:
                    if receiving_category not in reconciliation_impact:
                        reconciliation_impact[receiving_category] = {
                            'cross_category_indents': 0,
                            'transactions': []
                        }
                
                # Cross-category indent: Negative for giving category
                reconciliation_impact[indent_category]['cross_category_indents'] -= indent_value
                reconciliation_impact[indent_category]['transactions'].append({
                    'type': 'outbound',
                    'amount': -indent_value,
                    'workarea': workarea,
                    'subcategory': subcategory,
                    'receiving_categories': cross_category_owners
                })
                
                # Positive for each receiving category (equal distribution)
                distributed_value = indent_value / len(cross_category_owners)
                for receiving_category in cross_category_owners:
                    reconciliation_impact[receiving_category]['cross_category_indents'] += distributed_value
                    reconciliation_impact[receiving_category]['transactions'].append({
                        'type': 'inbound',
                        'amount': distributed_value,
                        'workarea': workarea,
                        'source_category': indent_category,
                        'source_subcategory': subcategory
                    })
                
                # Log transfer details
                transfer_details_log.append({
                    'source_category': indent_category,
                    'source_subcategory': subcategory,
                    'workarea': workarea,
                    'receiving_categories': cross_category_owners,
                    'total_amount': indent_value,
                    'distributed_amount': distributed_value
                })
    
    return {
        'reconciliation_impact': reconciliation_impact,
        'transfer_details': transfer_details_log,
        'total_transactions': len(transfer_details_log)
    }

def format_indian_currency(value: float) -> str:
    """Format currency in Indian numbering system."""
    if abs(value) < 0.01:
        return "-"
    
    is_negative = value < 0
    abs_value = abs(value)
    
    # Format with commas
    formatted = f"{abs_value:,.0f}"
    
    return f"-{formatted}" if is_negative else formatted

def explain_category_calculation(category_name: str, consumption_df: pd.DataFrame, category_workarea_mappings: List[Dict]) -> None:
    """
    Provide detailed explanation of how a specific category's cross-category indent value is calculated.
    """
    print(f"\n" + "=" * 80)
    print(f"DETAILED CALCULATION BREAKDOWN FOR {category_name} CATEGORY")
    print("=" * 80)

    # Build mappings
    mapped_categories = set()
    category_to_workareas = {}

    for mapping in category_workarea_mappings:
        category_name_map = mapping['categoryName']
        work_areas = mapping['workAreas']
        if category_name_map and work_areas:
            mapped_categories.add(category_name_map)
            category_to_workareas[category_name_map] = work_areas

    mapped_category_names = {cat for cat in mapped_categories if cat != 'OTHERS'}

    print(f"\n1. Category Mapping Analysis:")
    print(f"   {category_name} maps to workareas: {category_to_workareas.get(category_name, 'Not found')}")

    # Find all transactions involving this category
    giving_transactions = []  # Where this category gives to others
    receiving_transactions = []  # Where this category receives from others

    # Collect indent data
    indent_data = {}

    for _, row in consumption_df.iterrows():
        original_category = str(row.get('Category', 'Others')).strip()
        category = original_category
        subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
        workarea = str(row.get('WorkArea', '')).strip()

        # Skip invalid entries
        if (not category or category == 'nan' or category == 'None' or
            not subcategory or subcategory == 'nan' or subcategory == 'None' or
            not workarea):
            continue

        # Skip unmapped categories
        if category not in mapped_category_names:
            continue

        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0

            if abs(indent_value) > 0.01:
                if workarea not in indent_data:
                    indent_data[workarea] = {}
                if category not in indent_data[workarea]:
                    indent_data[workarea][category] = {}
                if subcategory not in indent_data[workarea][category]:
                    indent_data[workarea][category][subcategory] = 0
                indent_data[workarea][category][subcategory] += indent_value

        except (ValueError, TypeError):
            continue

    # Analyze cross-category flows
    total_given = 0
    total_received = 0

    print(f"\n2. Raw Data Analysis for {category_name}:")
    print("-" * 60)

    # Show raw data for this category first
    category_raw_data = {}
    for _, row in consumption_df.iterrows():
        original_category = str(row.get('Category', 'Others')).strip()
        category = original_category
        subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
        workarea = str(row.get('WorkArea', '')).strip()

        if category == category_name:
            try:
                unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
                indent_qty = float(row.get('WorkArea Indent', 0) or 0)
                indent_value = indent_qty * unit_price if unit_price > 0 else 0

                if abs(indent_value) > 0.01:
                    key = f"{workarea}|{subcategory}"
                    if key not in category_raw_data:
                        category_raw_data[key] = []
                    category_raw_data[key].append({
                        'indent_qty': indent_qty,
                        'unit_price': unit_price,
                        'indent_value': indent_value
                    })
            except (ValueError, TypeError):
                continue

    print(f"   Raw indent data for {category_name}:")
    for key, records in category_raw_data.items():
        workarea, subcategory = key.split('|')
        total_value = sum(r['indent_value'] for r in records)
        print(f"   {workarea} → {subcategory}: ₹{total_value:,.2f} ({len(records)} records)")

    print(f"\n3. Cross-Category Flow Analysis:")
    print("-" * 60)

    for workarea, workarea_categories in indent_data.items():
        # Find workarea owners
        workarea_owner_categories = []
        for cat_name, work_areas in category_to_workareas.items():
            if workarea in work_areas:
                workarea_owner_categories.append(cat_name)

        if not workarea_owner_categories:
            continue

        # Check transactions involving our target category
        for indent_category, subcategories in workarea_categories.items():
            for subcategory, indent_value in subcategories.items():
                cross_category_owners = [owner for owner in workarea_owner_categories if owner != indent_category]

                if not cross_category_owners:
                    continue

                # Case 1: Our category is giving (source)
                if indent_category == category_name:
                    distributed_value = indent_value / len(cross_category_owners)
                    total_given += indent_value

                    giving_transactions.append({
                        'workarea': workarea,
                        'subcategory': subcategory,
                        'total_amount': indent_value,
                        'receiving_categories': cross_category_owners,
                        'distributed_per_receiver': distributed_value
                    })

                    print(f"\n   GIVING Transaction:")
                    print(f"   Workarea: {workarea}")
                    print(f"   Subcategory: {subcategory}")
                    print(f"   Amount Given: ₹{indent_value:,.2f}")
                    print(f"   Receiving Categories: {cross_category_owners}")
                    print(f"   Impact on {category_name}: -₹{indent_value:,.2f}")

                # Case 2: Our category is receiving
                elif category_name in cross_category_owners:
                    distributed_value = indent_value / len(cross_category_owners)
                    total_received += distributed_value

                    receiving_transactions.append({
                        'workarea': workarea,
                        'source_category': indent_category,
                        'subcategory': subcategory,
                        'total_amount': indent_value,
                        'received_amount': distributed_value
                    })

                    print(f"\n   RECEIVING Transaction:")
                    print(f"   Workarea: {workarea}")
                    print(f"   Source Category: {indent_category}")
                    print(f"   Source Subcategory: {subcategory}")
                    print(f"   Total Amount: ₹{indent_value:,.2f}")
                    print(f"   Receivers: {cross_category_owners}")
                    print(f"   {category_name} Receives: ₹{distributed_value:,.2f}")

    # Final calculation
    net_impact = total_received - total_given

    print(f"\n3. Final Calculation Summary:")
    print("-" * 60)
    print(f"   Total Given by {category_name}: ₹{total_given:,.2f}")
    print(f"   Total Received by {category_name}: ₹{total_received:,.2f}")
    print(f"   Net Impact: ₹{total_received:,.2f} - ₹{total_given:,.2f} = ₹{net_impact:,.2f}")
    print(f"   Formatted (Indian): {format_indian_currency(net_impact)}")

    print(f"\n4. Transaction Summary:")
    print(f"   Giving Transactions: {len(giving_transactions)}")
    print(f"   Receiving Transactions: {len(receiving_transactions)}")
    print(f"   Total Transactions: {len(giving_transactions) + len(receiving_transactions)}")

def main():
    parser = argparse.ArgumentParser(description='Show detailed cross-category indent calculation breakdown')
    parser.add_argument('--csv', required=True, help='Path to consumption CSV file')
    parser.add_argument('--config', help='Path to mapping configuration JSON file')
    parser.add_argument('--tenant-id', help='Tenant ID for database lookup')
    parser.add_argument('--category', required=True, help='Category name to show detailed breakdown for')

    args = parser.parse_args()

    # Load consumption data (silently)
    try:
        consumption_df = pd.read_csv(args.csv)
    except Exception as e:
        print(f"Error loading data: {e}")
        sys.exit(1)

    # Load mapping configuration (silently)
    mapping_config = load_mapping_config(args.config, args.tenant_id)
    if not mapping_config:
        print("Could not load mapping configuration")
        sys.exit(1)

    # Build category-workarea mappings (silently)
    category_workarea_mappings = build_category_workarea_mappings(mapping_config)

    # Show only the detailed explanation
    explain_category_calculation(args.category, consumption_df, category_workarea_mappings)

if __name__ == "__main__":
    main()
