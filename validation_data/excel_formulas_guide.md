
# Excel Formulas for Manual Validation

## Step 1: Calculate Indent Value
Add this formula in a new column (assuming WorkArea Indent is in column F and WAC is in column G):
```
=IF(AND(NOT(ISBLANK(F2)), NOT(ISBLANK(G2)), F2>0, G2>0), F2*G2, 0)
```

## Step 2: Identify BEVERAGES Giving Transactions
Add this formula to identify when BEVERAGES is giving (negative impact):
```
=IF(AND(A2="BEVERAGES", H2>0.01), -H2, 0)
```

## Step 3: Identify FOOD to BAR Transactions (BEVERAGES receives 50%)
```
=IF(AND(A2="FOOD", C2="BAR", H2>0.01), H2/2, 0)
```

## Step 4: Identify LIQUOR to BAR Transactions (BEVERAGES receives 100%)
```
=IF(AND(A2="LIQUOR", C2="BAR", H2>0.01), H2, 0)
```

## Step 5: Calculate Total Impact on BEVERAGES
Sum all the above columns to get the net impact on BEVERAGES category.

## Expected Results:
- BEVERAGES Giving: approximately -₹1,38,171
- FOOD to BAR (50%): approximately +₹35,287  
- LIQUOR to BAR (100%): approximately +₹3,81,736
- Net Impact: approximately +₹2,78,853

## Verification Steps:
1. Check that all indent values are calculated correctly (Quantity × Price)
2. Verify that BAR workarea transactions are properly identified
3. Confirm that FOOD to BAR is split 50/50 between LIQUOR and BEVERAGES
4. Confirm that LIQUOR to BAR goes 100% to BEVERAGES
5. Sum all impacts to get final result
